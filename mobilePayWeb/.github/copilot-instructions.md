<!-- Use this file to provide workspace-specific custom instructions to Copilot. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# 移动支付后台管理系统

这是一个使用 React + TypeScript + Vite 构建的现代化移动支付后台管理系统。

## 项目特点

- 使用 Vite 作为构建工具，提供快速的开发体验
- React 18 + TypeScript 确保类型安全
- 现代化的后台管理界面设计，支持响应式布局
- 包含完整的支付业务管理功能模块
- 专业的数据展示和管理界面

## 功能模块

- **仪表盘**：数据概览、实时统计、图表展示
- **交易管理**：交易记录、状态管理、退款操作
- **用户管理**：用户信息、账户状态、权限控制
- **系统设置**：参数配置、安全设置、系统监控

## 开发指南

- 优先使用 TypeScript 进行开发
- 组件采用函数式组件 + Hooks 模式
- 样式使用 CSS 模块化，避免样式冲突
- 注重后台管理系统的用户体验和数据展示
- 保持代码简洁和可维护性
- 遵循后台管理系统的设计规范

## 设计原则

- **数据驱动**：以数据展示为核心的界面设计
- **功能导向**：注重管理功能的实用性和效率
- **权限控制**：考虑不同角色的访问权限
- **响应式设计**：适配不同屏幕尺寸的管理终端

## 代码规范

- 使用 ESLint 进行代码检查
- 遵循 React 最佳实践
- 保持组件单一职责原则
- 注重无障碍访问性
- 统一的命名规范和代码风格
