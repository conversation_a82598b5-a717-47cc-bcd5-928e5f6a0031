{"name": "eslint", "version": "8.57.1", "author": "<PERSON> <<EMAIL>>", "description": "An AST-based pattern checker for JavaScript.", "bin": {"eslint": "./bin/eslint.js"}, "main": "./lib/api.js", "exports": {"./package.json": "./package.json", ".": "./lib/api.js", "./use-at-your-own-risk": "./lib/unsupported-api.js"}, "scripts": {"build:docs:update-links": "node tools/fetch-docs-links.js", "build:site": "node Makefile.js gensite", "build:webpack": "node Makefile.js webpack", "build:readme": "node tools/update-readme.js", "lint": "node Makefile.js lint", "lint:docs:js": "node Makefile.js lintDocsJS", "lint:docs:rule-examples": "node Makefile.js checkRuleExamples", "lint:fix": "node Makefile.js lint -- fix", "lint:fix:docs:js": "node Makefile.js lintDocsJS -- fix", "release:generate:alpha": "node Makefile.js generatePrerelease -- alpha", "release:generate:beta": "node Makefile.js generatePrerelease -- beta", "release:generate:latest": "node Makefile.js generateRelease -- latest", "release:generate:maintenance": "node Makefile.js generateRelease -- maintenance", "release:generate:rc": "node Makefile.js generatePrerelease -- rc", "release:publish": "node Makefile.js publishRelease", "test": "node Makefile.js test", "test:cli": "mocha", "test:fuzz": "node Makefile.js fuzz", "test:performance": "node Makefile.js perf"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": "eslint --fix", "*.md": "markdownlint --fix", "lib/rules/*.js": ["node tools/update-eslint-all.js", "git add packages/js/src/configs/eslint-all.js"], "docs/src/rules/*.md": ["node tools/check-rule-examples.js", "node tools/fetch-docs-links.js", "git add docs/src/_data/further_reading_links.json"], "docs/**/*.svg": "npx svgo -r --multipass"}, "files": ["LICENSE", "README.md", "bin", "conf", "lib", "messages"], "repository": "eslint/eslint", "funding": "https://opencollective.com/eslint", "homepage": "https://eslint.org", "bugs": "https://github.com/eslint/eslint/issues/", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.57.1", "@humanwhocodes/config-array": "^0.13.0", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "devDependencies": {"@babel/core": "^7.4.3", "@babel/preset-env": "^7.4.3", "@sinonjs/fake-timers": "11.2.2", "@wdio/browser-runner": "^8.14.6", "@wdio/cli": "^8.14.6", "@wdio/concise-reporter": "^8.14.0", "@wdio/globals": "^8.14.6", "@wdio/mocha-framework": "^8.14.0", "babel-loader": "^8.0.5", "c8": "^7.12.0", "chai": "^4.0.1", "cheerio": "^0.22.0", "common-tags": "^1.8.0", "core-js": "^3.1.3", "ejs": "^3.0.2", "eslint": "file:.", "eslint-config-eslint": "file:packages/eslint-config-eslint", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-eslint-plugin": "^5.2.1", "eslint-plugin-internal-rules": "file:tools/internal-rules", "eslint-plugin-jsdoc": "^46.2.5", "eslint-plugin-n": "^16.6.0", "eslint-plugin-unicorn": "^49.0.0", "eslint-release": "^3.3.0", "eslump": "^3.0.0", "esprima": "^4.0.1", "fast-glob": "^3.2.11", "fs-teardown": "^0.1.3", "glob": "^7.1.6", "got": "^11.8.3", "gray-matter": "^4.0.3", "lint-staged": "^11.0.0", "load-perf": "^0.2.0", "markdown-it": "^12.2.0", "markdown-it-container": "^3.0.0", "markdownlint": "^0.32.0", "markdownlint-cli": "^0.37.0", "marked": "^4.0.8", "memfs": "^3.0.1", "metascraper": "^5.25.7", "metascraper-description": "^5.25.7", "metascraper-image": "^5.29.3", "metascraper-logo": "^5.25.7", "metascraper-logo-favicon": "^5.25.7", "metascraper-title": "^5.25.7", "mocha": "^8.3.2", "mocha-junit-reporter": "^2.0.0", "node-polyfill-webpack-plugin": "^1.0.3", "npm-license": "^0.3.3", "pirates": "^4.0.5", "progress": "^2.0.3", "proxyquire": "^2.0.1", "recast": "^0.23.0", "regenerator-runtime": "^0.14.0", "rollup-plugin-node-polyfills": "^0.2.1", "semver": "^7.5.3", "shelljs": "^0.8.2", "sinon": "^11.0.0", "vite-plugin-commonjs": "0.10.1", "webdriverio": "^8.14.6", "webpack": "^5.23.0", "webpack-cli": "^4.5.0", "yorkie": "^2.0.0"}, "keywords": ["ast", "lint", "javascript", "ecmascript", "espree"], "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}