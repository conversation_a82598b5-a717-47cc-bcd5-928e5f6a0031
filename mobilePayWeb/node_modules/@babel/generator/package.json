{"_from": "@babel/generator@^7.28.0", "_id": "@babel/generator@7.28.0", "_inBundle": false, "_integrity": "sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==", "_location": "/@babel/generator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/generator@^7.28.0", "name": "@babel/generator", "escapedName": "@babel%2fgenerator", "scope": "@babel", "rawSpec": "^7.28.0", "saveSpec": null, "fetchSpec": "^7.28.0"}, "_requiredBy": ["/@babel/core", "/@babel/traverse"], "_resolved": "https://registry.npmmirror.com/@babel/generator/-/generator-7.28.0.tgz", "_shasum": "9cc2f7bd6eb054d77dc66c2664148a0c5118acd2", "_spec": "@babel/generator@^7.28.0", "_where": "/Users/<USER>/Desktop/mobilePayWeb/node_modules/@babel/core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/parser": "^7.28.0", "@babel/types": "^7.28.0", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "deprecated": false, "description": "Turns an AST into code.", "devDependencies": {"@babel/core": "^7.28.0", "@babel/helper-fixtures": "^7.28.0", "@babel/plugin-transform-typescript": "^7.28.0", "@jridgewell/sourcemap-codec": "^1.5.3", "@types/jsesc": "^2.5.0", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "files": ["lib"], "homepage": "https://babel.dev/docs/en/next/babel-generator", "license": "MIT", "main": "./lib/index.js", "name": "@babel/generator", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-generator"}, "type": "commonjs", "version": "7.28.0"}