{"_from": "@babel/compat-data@^7.27.2", "_id": "@babel/compat-data@7.28.0", "_inBundle": false, "_integrity": "sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==", "_location": "/@babel/compat-data", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/compat-data@^7.27.2", "name": "@babel/compat-data", "escapedName": "@babel%2fcompat-data", "scope": "@babel", "rawSpec": "^7.27.2", "saveSpec": null, "fetchSpec": "^7.27.2"}, "_requiredBy": ["/@babel/helper-compilation-targets"], "_resolved": "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.28.0.tgz", "_shasum": "9fc6fd58c2a6a15243cd13983224968392070790", "_spec": "@babel/compat-data@^7.27.2", "_where": "/Users/<USER>/Desktop/mobilePayWeb/node_modules/@babel/helper-compilation-targets", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The compat-data to determine required Babel plugins", "devDependencies": {"@mdn/browser-compat-data": "^6.0.8", "core-js-compat": "^3.43.0", "electron-to-chromium": "^1.5.140"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js", "./overlapping-plugins": "./overlapping-plugins.js", "./plugin-bugfixes": "./plugin-bugfixes.js"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "compat-table", "compat-data"], "license": "MIT", "name": "@babel/compat-data", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-compat-data"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.mjs && node ./scripts/build-modules-support.mjs && node ./scripts/build-bugfixes-targets.mjs"}, "type": "commonjs", "version": "7.28.0"}