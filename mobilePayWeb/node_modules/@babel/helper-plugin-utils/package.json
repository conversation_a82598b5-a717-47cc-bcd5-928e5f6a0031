{"_from": "@babel/helper-plugin-utils@^7.27.1", "_id": "@babel/helper-plugin-utils@7.27.1", "_inBundle": false, "_integrity": "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==", "_location": "/@babel/helper-plugin-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-plugin-utils@^7.27.1", "name": "@babel/helper-plugin-utils", "escapedName": "@babel%2fhelper-plugin-utils", "scope": "@babel", "rawSpec": "^7.27.1", "saveSpec": null, "fetchSpec": "^7.27.1"}, "_requiredBy": ["/@babel/plugin-transform-react-jsx-self", "/@babel/plugin-transform-react-jsx-source"], "_resolved": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "_shasum": "ddb2f876534ff8013e6c2b299bf4d39b3c51d44c", "_spec": "@babel/helper-plugin-utils@^7.27.1", "_where": "/Users/<USER>/Desktop/mobilePayWeb/node_modules/@babel/plugin-transform-react-jsx-self", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "General utilities for plugins to use", "devDependencies": {"@babel/core": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-plugin-utils", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-plugin-utils", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-plugin-utils"}, "type": "commonjs", "version": "7.27.1"}