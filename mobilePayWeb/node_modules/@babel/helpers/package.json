{"_from": "@babel/helpers@^7.27.6", "_id": "@babel/helpers@7.28.2", "_inBundle": false, "_integrity": "sha512-/V9771t+EgXz62aCcyofnQhGM8DQACbRhvzKFsXKC9QM+5MadF8ZmIm0crDMaz3+o0h0zXfJnd4EhbYbxsrcFw==", "_location": "/@babel/helpers", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helpers@^7.27.6", "name": "@babel/helpers", "escapedName": "@babel%2fhelpers", "scope": "@babel", "rawSpec": "^7.27.6", "saveSpec": null, "fetchSpec": "^7.27.6"}, "_requiredBy": ["/@babel/core"], "_resolved": "https://registry.npmmirror.com/@babel/helpers/-/helpers-7.28.2.tgz", "_shasum": "80f0918fecbfebea9af856c419763230040ee850", "_spec": "@babel/helpers@^7.27.6", "_where": "/Users/<USER>/Desktop/mobilePayWeb/node_modules/@babel/core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.28.2"}, "deprecated": false, "description": "Collection of helper functions used by Babel transforms.", "devDependencies": {"@babel/generator": "^7.28.0", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/parser": "^7.28.0", "regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helpers", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helpers", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helpers"}, "type": "commonjs", "version": "7.28.2"}