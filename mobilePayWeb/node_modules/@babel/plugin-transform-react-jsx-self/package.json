{"_from": "@babel/plugin-transform-react-jsx-self@^7.27.1", "_id": "@babel/plugin-transform-react-jsx-self@7.27.1", "_inBundle": false, "_integrity": "sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==", "_location": "/@babel/plugin-transform-react-jsx-self", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-react-jsx-self@^7.27.1", "name": "@babel/plugin-transform-react-jsx-self", "escapedName": "@babel%2fplugin-transform-react-jsx-self", "scope": "@babel", "rawSpec": "^7.27.1", "saveSpec": null, "fetchSpec": "^7.27.1"}, "_requiredBy": ["/@vitejs/plugin-react"], "_resolved": "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz", "_shasum": "af678d8506acf52c577cac73ff7fe6615c85fc92", "_spec": "@babel/plugin-transform-react-jsx-self@^7.27.1", "_where": "/Users/<USER>/Desktop/mobilePayWeb/node_modules/@vitejs/plugin-react", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "deprecated": false, "description": "Add a __self prop to all JSX Elements", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx-self", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-react-jsx-self", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-react-jsx-self"}, "type": "commonjs", "version": "7.27.1"}