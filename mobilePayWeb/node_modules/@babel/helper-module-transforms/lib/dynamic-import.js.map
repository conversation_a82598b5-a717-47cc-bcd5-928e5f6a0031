{"version": 3, "names": ["_core", "require", "exports", "getDynamicImportSource", "node", "source", "arguments", "t", "isStringLiteral", "isTemplateLiteral", "template", "expression", "ast", "buildDynamicImport", "deferT<PERSON><PERSON><PERSON>", "wrapWithPromise", "builder", "specifier", "isCallExpression", "quasis", "length", "specifierToString", "identifier", "templateLiteral", "templateElement", "raw"], "sources": ["../src/dynamic-import.ts"], "sourcesContent": ["// Heavily inspired by\n// https://github.com/airbnb/babel-plugin-dynamic-import-node/blob/master/src/utils.js\n\nimport { types as t, template } from \"@babel/core\";\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n  // eslint-disable-next-line no-restricted-globals\n  exports.getDynamicImportSource = function getDynamicImportSource(\n    node: t.CallExpression,\n  ): t.StringLiteral | t.TemplateLiteral {\n    const [source] = node.arguments;\n\n    return t.isStringLiteral(source) || t.isTemplateLiteral(source)\n      ? source\n      : (template.expression.ast`\\`\\${${source}}\\`` as t.TemplateLiteral);\n  };\n}\n\nexport function buildDynamicImport(\n  node: t.CallExpression | t.ImportExpression,\n  deferToThen: boolean,\n  wrapWithPromise: boolean,\n  builder: (specifier: t.Expression) => t.Expression,\n): t.Expression {\n  const specifier = t.isCallExpression(node) ? node.arguments[0] : node.source;\n\n  if (\n    t.isStringLiteral(specifier) ||\n    (t.isTemplateLiteral(specifier) && specifier.quasis.length === 0)\n  ) {\n    if (deferToThen) {\n      return template.expression.ast`\n        Promise.resolve().then(() => ${builder(specifier)})\n      `;\n    } else return builder(specifier);\n  }\n\n  const specifierToString = t.isTemplateLiteral(specifier)\n    ? t.identifier(\"specifier\")\n    : t.templateLiteral(\n        [t.templateElement({ raw: \"\" }), t.templateElement({ raw: \"\" })],\n        [t.identifier(\"specifier\")],\n      );\n\n  if (deferToThen) {\n    return template.expression.ast`\n      (specifier =>\n        new Promise(r => r(${specifierToString}))\n          .then(s => ${builder(t.identifier(\"s\"))})\n      )(${specifier})\n    `;\n  } else if (wrapWithPromise) {\n    return template.expression.ast`\n      (specifier =>\n        new Promise(r => r(${builder(specifierToString)}))\n      )(${specifier})\n    `;\n  } else {\n    return template.expression.ast`\n      (specifier => ${builder(specifierToString)})(${specifier})\n    `;\n  }\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,KAAA,GAAAC,OAAA;AAEiE;EAE/DC,OAAO,CAACC,sBAAsB,GAAG,SAASA,sBAAsBA,CAC9DC,IAAsB,EACe;IACrC,MAAM,CAACC,MAAM,CAAC,GAAGD,IAAI,CAACE,SAAS;IAE/B,OAAOC,WAAC,CAACC,eAAe,CAACH,MAAM,CAAC,IAAIE,WAAC,CAACE,iBAAiB,CAACJ,MAAM,CAAC,GAC3DA,MAAM,GACLK,cAAQ,CAACC,UAAU,CAACC,GAAG,QAAQP,MAAM,KAA2B;EACvE,CAAC;AACH;AAEO,SAASQ,kBAAkBA,CAChCT,IAA2C,EAC3CU,WAAoB,EACpBC,eAAwB,EACxBC,OAAkD,EACpC;EACd,MAAMC,SAAS,GAAGV,WAAC,CAACW,gBAAgB,CAACd,IAAI,CAAC,GAAGA,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,GAAGF,IAAI,CAACC,MAAM;EAE5E,IACEE,WAAC,CAACC,eAAe,CAACS,SAAS,CAAC,IAC3BV,WAAC,CAACE,iBAAiB,CAACQ,SAAS,CAAC,IAAIA,SAAS,CAACE,MAAM,CAACC,MAAM,KAAK,CAAE,EACjE;IACA,IAAIN,WAAW,EAAE;MACf,OAAOJ,cAAQ,CAACC,UAAU,CAACC,GAAG;AACpC,uCAAuCI,OAAO,CAACC,SAAS,CAAC;AACzD,OAAO;IACH,CAAC,MAAM,OAAOD,OAAO,CAACC,SAAS,CAAC;EAClC;EAEA,MAAMI,iBAAiB,GAAGd,WAAC,CAACE,iBAAiB,CAACQ,SAAS,CAAC,GACpDV,WAAC,CAACe,UAAU,CAAC,WAAW,CAAC,GACzBf,WAAC,CAACgB,eAAe,CACf,CAAChB,WAAC,CAACiB,eAAe,CAAC;IAAEC,GAAG,EAAE;EAAG,CAAC,CAAC,EAAElB,WAAC,CAACiB,eAAe,CAAC;IAAEC,GAAG,EAAE;EAAG,CAAC,CAAC,CAAC,EAChE,CAAClB,WAAC,CAACe,UAAU,CAAC,WAAW,CAAC,CAC5B,CAAC;EAEL,IAAIR,WAAW,EAAE;IACf,OAAOJ,cAAQ,CAACC,UAAU,CAACC,GAAG;AAClC;AACA,6BAA6BS,iBAAiB;AAC9C,uBAAuBL,OAAO,CAACT,WAAC,CAACe,UAAU,CAAC,GAAG,CAAC,CAAC;AACjD,UAAUL,SAAS;AACnB,KAAK;EACH,CAAC,MAAM,IAAIF,eAAe,EAAE;IAC1B,OAAOL,cAAQ,CAACC,UAAU,CAACC,GAAG;AAClC;AACA,6BAA6BI,OAAO,CAACK,iBAAiB,CAAC;AACvD,UAAUJ,SAAS;AACnB,KAAK;EACH,CAAC,MAAM;IACL,OAAOP,cAAQ,CAACC,UAAU,CAACC,GAAG;AAClC,sBAAsBI,OAAO,CAACK,iBAAiB,CAAC,KAAKJ,SAAS;AAC9D,KAAK;EACH;AACF", "ignoreList": []}