"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Var = exports.User = exports.Statement = exports.SpreadProperty = exports.Scope = exports.RestProperty = exports.ReferencedMemberExpression = exports.ReferencedIdentifier = exports.Referenced = exports.Pure = exports.NumericLiteralTypeAnnotation = exports.Generated = exports.ForAwaitStatement = exports.Flow = exports.Expression = exports.ExistentialTypeParam = exports.BlockScoped = exports.BindingIdentifier = void 0;
const ReferencedIdentifier = exports.ReferencedIdentifier = ["Identifier", "JSXIdentifier"];
const ReferencedMemberExpression = exports.ReferencedMemberExpression = ["MemberExpression"];
const BindingIdentifier = exports.BindingIdentifier = ["Identifier"];
const Statement = exports.Statement = ["Statement"];
const Expression = exports.Expression = ["Expression"];
const Scope = exports.Scope = ["Scopable", "Pattern"];
const Referenced = exports.Referenced = null;
const BlockScoped = exports.BlockScoped = null;
const Var = exports.Var = ["VariableDeclaration"];
const User = exports.User = null;
const Generated = exports.Generated = null;
const Pure = exports.Pure = null;
const Flow = exports.Flow = ["Flow", "ImportDeclaration", "ExportDeclaration", "ImportSpecifier"];
const RestProperty = exports.RestProperty = ["RestElement"];
const SpreadProperty = exports.SpreadProperty = ["RestElement"];
const ExistentialTypeParam = exports.ExistentialTypeParam = ["ExistsTypeAnnotation"];
const NumericLiteralTypeAnnotation = exports.NumericLiteralTypeAnnotation = ["NumberLiteralTypeAnnotation"];
const ForAwaitStatement = exports.ForAwaitStatement = ["ForOfStatement"];

//# sourceMappingURL=virtual-types.js.map
