{"version": "2.0.0", "tasks": [{"label": "开发服务器", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "启动项目 (PowerShell)", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "./run.ps1"], "group": "build", "isBackground": false, "problemMatcher": []}, {"label": "启动项目 (批处理)", "type": "shell", "command": "./启动项目.bat", "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "类型检查", "type": "shell", "command": "npx", "args": ["tsc", "--noEmit"], "group": "build", "isBackground": false, "problemMatcher": ["$tsc"], "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "构建项目", "type": "shell", "command": "npm", "args": ["run", "build"], "group": "build", "isBackground": false, "problemMatcher": ["$tsc"]}]}